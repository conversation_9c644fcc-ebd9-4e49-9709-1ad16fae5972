"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileSignature, Download, Share, Edit, RefreshCw } from "lucide-react";
import { SignatureRequestModal } from "../components/SignatureRequestModal";
import { regenerateDocumentWithSignatures } from "../actions";

interface Contact {
  id: string;
  name: string;
  email: any; // JSONB field
}

interface Signature {
  id: string;
  signature_status: string | null;
  contact_id: string;
  signed_at?: string | null;
  requested_at: string | null;
}

interface DocumentViewClientProps {
  documentId: string;
  documentName: string;
  requiresSignature: boolean;
  contacts: Contact[];
  signatures: Signature[];
}

export function DocumentViewClient({
  documentId,
  documentName,
  requiresSignature,
  contacts,
  signatures,
}: DocumentViewClientProps) {
  const [showSignatureModal, setShowSignatureModal] = useState(false);
  const [isRegenerating, setIsRegenerating] = useState(false);

  const handleRegenerateDocument = async () => {
    setIsRegenerating(true);
    try {
      const result = await regenerateDocumentWithSignatures(documentId);
      if (result.success) {
        // Refresh the page to show updated content
        window.location.reload();
      } else {
        alert(result.error || "Failed to regenerate document");
      }
    } catch (error) {
      alert("An error occurred while regenerating the document");
    } finally {
      setIsRegenerating(false);
    }
  };

  const handleDownload = () => {
    // TODO: Implement download functionality
    alert("Download functionality coming soon!");
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    alert("Share functionality coming soon!");
  };

  const handleEdit = () => {
    // TODO: Implement edit functionality
    alert("Edit functionality coming soon!");
  };

  // Check if there are any signed signatures
  const hasSignedSignatures = signatures.some(sig => sig.signature_status === "signed");

  return (
    <div className="flex items-center gap-2">
      {requiresSignature && (
        <>
          <Button 
            className="bg-blue-600 hover:bg-blue-700"
            onClick={() => setShowSignatureModal(true)}
          >
            <FileSignature className="h-4 w-4 mr-2" />
            Request Signature
          </Button>
          
          {hasSignedSignatures && (
            <Button 
              variant="outline"
              onClick={handleRegenerateDocument}
              disabled={isRegenerating}
              className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRegenerating ? 'animate-spin' : ''}`} />
              {isRegenerating ? "Updating..." : "Update with Signatures"}
            </Button>
          )}
        </>
      )}
      
      <Button variant="outline" onClick={handleDownload}>
        <Download className="h-4 w-4 mr-2" />
        Download
      </Button>
      
      <Button variant="outline" onClick={handleShare}>
        <Share className="h-4 w-4 mr-2" />
        Share
      </Button>
      
      <Button variant="outline" onClick={handleEdit}>
        <Edit className="h-4 w-4 mr-2" />
        Edit
      </Button>

      {/* Signature Request Modal */}
      <SignatureRequestModal
        open={showSignatureModal}
        onOpenChange={setShowSignatureModal}
        documentId={documentId}
        documentName={documentName}
        contacts={contacts}
      />
    </div>
  );
}
