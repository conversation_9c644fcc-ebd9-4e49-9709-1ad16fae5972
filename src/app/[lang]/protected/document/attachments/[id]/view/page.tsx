import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, FileSignature, Download, Share, Edit } from "lucide-react";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";

interface DocumentViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function DocumentViewPage({ params }: DocumentViewPageProps) {
  const { lang, id } = await params;
  
  // Get current user profile
  const userProfile = await auth.getCurrentUserProfile();
  if (!userProfile?.organizationId) {
    notFound();
  }

  // Get document details
  const supabase = await createClient();
  const { data: document, error } = await supabase
    .from("document_attachments")
    .select("*")
    .eq("id", id)
    .eq("organization_id", userProfile.organizationId)
    .single();

  if (error || !document) {
    notFound();
  }

  // Extract metadata
  const metadata = (document.metadata as any) || {};
  const generatedContent = metadata.generated_content || "";
  const requiresSignature = metadata.requires_signature || false;
  const templateName = metadata.template_name || "Unknown Template";

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="../">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Documents
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{document.document_name}</h1>
            <p className="text-muted-foreground">Generated from: {templateName}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {requiresSignature && (
            <Button className="bg-blue-600 hover:bg-blue-700">
              <FileSignature className="h-4 w-4 mr-2" />
              Request Signature
            </Button>
          )}
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button variant="outline">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </div>
      </div>

      {/* Document Info */}
      <Card>
        <CardHeader>
          <CardTitle>Document Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Created:</span>
              <span className="ml-2 text-gray-900">
                {document.uploaded_at ? new Date(document.uploaded_at).toLocaleDateString() : "Unknown"}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Size:</span>
              <span className="ml-2 text-gray-900">
                {document.file_size ? (document.file_size / 1024).toFixed(1) : "0"} KB
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Type:</span>
              <span className="ml-2 text-gray-900">{document.document_type}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2 mt-4">
            {requiresSignature && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                Signature Required
              </Badge>
            )}
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              Generated Document
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Document Content */}
      <Card>
        <CardHeader>
          <CardTitle>Document Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-6 bg-white min-h-[600px] max-h-[800px] overflow-y-auto">
            {generatedContent ? (
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: generatedContent }}
              />
            ) : (
              <div className="text-center text-gray-500 py-12">
                <div className="text-6xl mb-4">📄</div>
                <h3 className="text-lg font-medium mb-2">No Content Available</h3>
                <p>The document content could not be loaded.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Signature Status (if applicable) */}
      {requiresSignature && (
        <Card>
          <CardHeader>
            <CardTitle>Signature Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Contact Signature</h4>
                  <p className="text-sm text-gray-600">Required for document completion</p>
                </div>
                <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                  Pending
                </Badge>
              </div>
              
              <div className="text-sm text-gray-600">
                <p>This document requires electronic signatures before it can be finalized.</p>
                <p>Click "Request Signature" to send signature requests to contacts.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Document Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Download className="h-6 w-6" />
              <span className="text-sm">Download PDF</span>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Share className="h-6 w-6" />
              <span className="text-sm">Share Link</span>
            </Button>
            
            {requiresSignature && (
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100">
                <FileSignature className="h-6 w-6" />
                <span className="text-sm">Manage Signatures</span>
              </Button>
            )}
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Edit className="h-6 w-6" />
              <span className="text-sm">Create Version</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
