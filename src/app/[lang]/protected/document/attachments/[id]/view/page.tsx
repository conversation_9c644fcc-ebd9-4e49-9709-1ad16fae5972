import { Suspense } from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, FileSignature, Download, Share, Edit, RefreshCw } from "lucide-react";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { getAvailableContacts, getDocumentSignatures } from "../actions";
import { DocumentViewClient } from "./DocumentViewClient";

interface DocumentViewPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

export default async function DocumentViewPage({ params }: DocumentViewPageProps) {
  const { lang, id } = await params;
  
  // Get current user profile
  const userProfile = await auth.getCurrentUserProfile();
  if (!userProfile?.organizationId) {
    notFound();
  }

  // Get document details
  const supabase = await createClient();
  const { data: document, error } = await supabase
    .from("document_attachments")
    .select("*")
    .eq("id", id)
    .eq("organization_id", userProfile.organizationId)
    .single();

  if (error || !document) {
    notFound();
  }

  // Extract metadata
  const metadata = (document.metadata as any) || {};
  const generatedContent = metadata.generated_content || "";
  const requiresSignature = metadata.requires_signature || false;
  const templateName = metadata.template_name || "Unknown Template";

  // Get contacts and signatures data
  const contactsResult = await getAvailableContacts();
  const signaturesResult = await getDocumentSignatures(id);

  const contacts = contactsResult.success ? (contactsResult.data || []) : [];
  const signatures = signaturesResult.success && 'data' in signaturesResult ? (signaturesResult.data || []) : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="../">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Documents
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{document.document_name}</h1>
            <p className="text-muted-foreground">Generated from: {templateName}</p>
          </div>
        </div>
        
        <DocumentViewClient
          documentId={id}
          documentName={document.document_name}
          requiresSignature={requiresSignature}
          contacts={contacts}
          signatures={signatures}
        />
      </div>

      {/* Document Info */}
      <Card>
        <CardHeader>
          <CardTitle>Document Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Created:</span>
              <span className="ml-2 text-gray-900">
                {document.uploaded_at ? new Date(document.uploaded_at).toLocaleDateString() : "Unknown"}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Size:</span>
              <span className="ml-2 text-gray-900">
                {document.file_size ? (document.file_size / 1024).toFixed(1) : "0"} KB
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Type:</span>
              <span className="ml-2 text-gray-900">{document.document_type}</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2 mt-4">
            {requiresSignature && (
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                Signature Required
              </Badge>
            )}
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              Generated Document
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Document Content */}
      <Card>
        <CardHeader>
          <CardTitle>Document Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-6 bg-white min-h-[600px] max-h-[800px] overflow-y-auto">
            {generatedContent ? (
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{ __html: generatedContent }}
              />
            ) : (
              <div className="text-center text-gray-500 py-12">
                <div className="text-6xl mb-4">📄</div>
                <h3 className="text-lg font-medium mb-2">No Content Available</h3>
                <p>The document content could not be loaded.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Signature Status (if applicable) */}
      {requiresSignature && (
        <Card>
          <CardHeader>
            <CardTitle>Signature Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {signatures.length > 0 ? (
                signatures.map((signature: any) => {
                  const contact = contacts.find((c: any) => c.id === signature.contact_id);
                  const getStatusBadge = (status: string) => {
                    switch (status) {
                      case "signed":
                        return <Badge className="bg-green-50 text-green-700 border-green-200">Signed</Badge>;
                      case "rejected":
                        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Rejected</Badge>;
                      case "expired":
                        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Expired</Badge>;
                      default:
                        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>;
                    }
                  };

                  const getContactEmail = (contact: any) => {
                    if (!contact?.email) return "No email";
                    if (typeof contact.email === 'object' && contact.email?.personal) {
                      return contact.email.personal;
                    }
                    if (typeof contact.email === 'string') {
                      return contact.email;
                    }
                    return "No email";
                  };

                  return (
                    <div key={signature.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h4 className="font-medium">
                          {contact ? contact.name : "Unknown Contact"}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {getContactEmail(contact)}
                        </p>
                        <p className="text-xs text-gray-500">
                          Requested: {new Date(signature.requested_at).toLocaleDateString()}
                          {signature.signed_at && (
                            <span> • Signed: {new Date(signature.signed_at).toLocaleDateString()}</span>
                          )}
                        </p>
                      </div>
                      {getStatusBadge(signature.signature_status)}
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileSignature className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h4 className="font-medium mb-2">No Signature Requests</h4>
                  <p className="text-sm">Click "Request Signature" to send signature requests to contacts.</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Document Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Download className="h-6 w-6" />
              <span className="text-sm">Download PDF</span>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Share className="h-6 w-6" />
              <span className="text-sm">Share Link</span>
            </Button>
            
            {requiresSignature && (
              <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100">
                <FileSignature className="h-6 w-6" />
                <span className="text-sm">Manage Signatures</span>
              </Button>
            )}
            
            <Button variant="outline" className="h-auto p-4 flex flex-col items-center gap-2">
              <Edit className="h-6 w-6" />
              <span className="text-sm">Create Version</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
