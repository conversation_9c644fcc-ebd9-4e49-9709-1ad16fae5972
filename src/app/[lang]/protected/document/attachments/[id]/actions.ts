"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { DocumentTemplateService } from "../../(features)/templates/lib/services/DocumentTemplateService";
import { DocumentSignatureService } from "../../(features)/signatures/lib/services/DocumentSignatureService";

/**
 * Create a signature request for a document
 */
export async function createSignatureRequest(
  documentId: string,
  contactId: string,
  expiresAt: Date,
  message?: string
) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    const signatureService = DocumentSignatureService.getInstance();
    const result = await signatureService.createSignatureRequest(
      {
        documentId,
        contactId,
        expiresAt,
        metadata: {
          message,
          requested_via: "document_view",
        },
      },
      userProfile.organizationId,
      userProfile.id
    );

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate relevant paths
    revalidatePath(`/protected/document/attachments/${documentId}`);
    revalidatePath("/protected/document/signatures");

    return {
      success: true,
      data: result.data,
      message: "Signature request created successfully",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create signature request",
    };
  }
}

/**
 * Regenerate document content with updated signatures
 */
export async function regenerateDocumentWithSignatures(documentId: string) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Get document details
    const supabase = await createClient();
    const { data: document, error: docError } = await supabase
      .from("document_attachments")
      .select("*")
      .eq("id", documentId)
      .eq("organization_id", userProfile.organizationId)
      .single();

    if (docError || !document) {
      return { success: false, error: "Document not found" };
    }

    const metadata = (document.metadata as any) || {};
    const templateId = metadata.template_id;
    const tokensUsed = metadata.tokens_used || {};

    if (!templateId) {
      return { success: false, error: "Document was not generated from a template" };
    }

    // Regenerate document with current signature data
    const templateService = new DocumentTemplateService();
    const result = await templateService.generateDocument({
      template_id: templateId,
      tokens: tokensUsed,
      output_format: "html",
      documentId,
      organizationId: userProfile.organizationId,
    });

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Update document with new content
    const { error: updateError } = await supabase
      .from("document_attachments")
      .update({
        metadata: {
          ...metadata,
          generated_content: result.data?.content,
          last_regenerated: new Date().toISOString(),
        },
        updated_at: new Date().toISOString(),
      })
      .eq("id", documentId)
      .eq("organization_id", userProfile.organizationId);

    if (updateError) {
      return { success: false, error: "Failed to update document" };
    }

    // Revalidate the document page
    revalidatePath(`/protected/document/attachments/${documentId}`);

    return {
      success: true,
      message: "Document regenerated with updated signatures",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to regenerate document",
    };
  }
}

/**
 * Get document signatures
 */
export async function getDocumentSignatures(documentId: string) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    const signatureService = DocumentSignatureService.getInstance();
    const result = await signatureService.getDocumentSignatures(
      documentId,
      userProfile.organizationId
    );

    return result;
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get document signatures",
    };
  }
}

/**
 * Get available contacts for signature requests
 */
export async function getAvailableContacts() {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    const supabase = await createClient();
    const { data: contacts, error } = await supabase
      .from("contacts")
      .select("id, name, email")
      .eq("organization_id", userProfile.organizationId)
      .eq("status", "active")
      .order("name");

    if (error) {
      return { success: false, error: error.message };
    }

    return {
      success: true,
      data: contacts || [],
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get contacts",
    };
  }
}
