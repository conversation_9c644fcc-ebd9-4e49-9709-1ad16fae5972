"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, FileSignature, Send } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { createSignatureRequest } from "../actions";

interface Contact {
  id: string;
  name: string;
  email: any; // JSONB field
}

interface SignatureRequestModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  documentId: string;
  documentName: string;
  contacts: Contact[];
}

export function SignatureRequestModal({
  open,
  onOpenChange,
  documentId,
  documentName,
  contacts,
}: SignatureRequestModalProps) {
  const [selectedContactId, setSelectedContactId] = useState("");
  const [expiresAt, setExpiresAt] = useState<Date>(
    new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
  );
  const [message, setMessage] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async () => {
    if (!selectedContactId) {
      setError("Please select a contact");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const result = await createSignatureRequest(
        documentId,
        selectedContactId,
        expiresAt,
        message
      );

      if (result.success) {
        onOpenChange(false);
        // Reset form
        setSelectedContactId("");
        setMessage("");
        setExpiresAt(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));
      } else {
        setError(result.error || "Failed to create signature request");
      }
    } catch (err) {
      setError("An error occurred while creating the signature request");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSignature className="h-5 w-5" />
            Request Electronic Signature
          </DialogTitle>
          <DialogDescription>
            Send a signature request for "{documentName}". The contact will receive an email
            with a secure link to sign the document electronically.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Contact Selection */}
          <div className="space-y-2">
            <Label htmlFor="contact">Contact</Label>
            <Select value={selectedContactId} onValueChange={setSelectedContactId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a contact to request signature from" />
              </SelectTrigger>
              <SelectContent>
                {contacts.map((contact) => {
                  const emailDisplay = typeof contact.email === 'object' && contact.email?.personal
                    ? contact.email.personal
                    : typeof contact.email === 'string'
                    ? contact.email
                    : 'No email';

                  return (
                    <SelectItem key={contact.id} value={contact.id}>
                      {contact.name} ({emailDisplay})
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Expiration Date */}
          <div className="space-y-2">
            <Label>Expiration Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !expiresAt && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {expiresAt ? format(expiresAt, "PPP") : <span>Pick an expiration date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={expiresAt}
                  onSelect={(date) => date && setExpiresAt(date)}
                  disabled={(date) => date < new Date()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <p className="text-sm text-muted-foreground">
              The signature request will expire on this date.
            </p>
          </div>

          {/* Custom Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Custom Message (Optional)</Label>
            <Textarea
              id="message"
              placeholder="Add a personal message to include with the signature request..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              This message will be included in the signature request email.
            </p>
          </div>

          {/* Error Display */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded p-3">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting || !selectedContactId}>
            {isSubmitting ? (
              "Sending..."
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                Send Request
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
