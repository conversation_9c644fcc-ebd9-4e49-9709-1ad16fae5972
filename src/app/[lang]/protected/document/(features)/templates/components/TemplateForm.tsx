"use client";

import { useState, useActionState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
// import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, Code, X, Plus } from "lucide-react";
import {
  DocumentTemplateWithMetadata,
  TemplateTokenGroup,
  TEMPLATE_TYPES,
  TEMPLATE_LANGUAGES,
} from "../lib/types";
import { createTemplate, CreateTemplateFormState } from "../actions/create";
import { updateTemplate, UpdateTemplateFormState } from "../actions/update";
import { DraggableTokenEditor } from "./DraggableTokenEditor";
import { getServices } from "../actions/services";

interface TemplateFormProps {
  template?: DocumentTemplateWithMetadata;
  availableTokens?: TemplateTokenGroup[];
  mode: "create" | "edit";
}

interface Service {
  id: string;
  name: string;
  description?: string | null;
  status: string;
}

const initialCreateState: CreateTemplateFormState = {
  success: false,
  message: "",
};

const initialUpdateState: UpdateTemplateFormState = {
  success: false,
  message: "",
};

export function TemplateForm({ template, availableTokens = [], mode }: TemplateFormProps) {
  const [createState, createAction] = useActionState(createTemplate, initialCreateState);
  const [updateState, updateAction] = useActionState(
    mode === "edit" && template ? updateTemplate.bind(null, template.id) : () => initialUpdateState,
    initialUpdateState
  );

  // Form state
  const [content, setContent] = useState(template?.content || "");
  const [isActive, setIsActive] = useState(template?.is_active ?? true);
  const [signatureRequired, setSignatureRequired] = useState(template?.signature_required ?? false);
  const [previewMode, setPreviewMode] = useState<"editor" | "preview">("editor");
  const [services, setServices] = useState<Service[]>([]);
  const [tags, setTags] = useState<string[]>((template?.metadata as any)?.tags || []);
  const [newTag, setNewTag] = useState("");

  const state = mode === "create" ? createState : updateState;
  const action = mode === "create" ? createAction : updateAction;

  // Load services on component mount
  useEffect(() => {
    const loadServices = async () => {
      const result = await getServices();
      if (result.success) {
        setServices(result.data);
      }
    };
    loadServices();
  }, []);

  // Handle tag management
  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  // Get template type label
  const getTemplateTypeLabel = (type: string) => {
    return type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  return (
    <form action={action} className="space-y-6">
      {/* Error/Success Messages */}
      {state.message && (
        <Alert variant={state.success ? "default" : "destructive"}>
          <AlertDescription>{state.message}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Template Name *</Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={template?.name}
                  placeholder="Enter template name"
                  required
                />
                {state.errors?.name && (
                  <p className="text-sm text-destructive mt-1">{state.errors.name[0]}</p>
                )}
              </div>

              <div>
                <Label htmlFor="template_type">Template Type *</Label>
                <Select name="template_type" defaultValue={template?.template_type}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select template type" />
                  </SelectTrigger>
                  <SelectContent>
                    {TEMPLATE_TYPES.map((type) => (
                      <SelectItem key={type} value={type}>
                        {getTemplateTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state.errors?.template_type && (
                  <p className="text-sm text-destructive mt-1">{state.errors.template_type[0]}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                defaultValue={(template?.metadata as any)?.description || ""}
                placeholder="Enter template description"
                rows={3}
              />
              {state.errors?.description && (
                <p className="text-sm text-destructive mt-1">{state.errors.description[0]}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="language">Language *</Label>
                <Select name="language" defaultValue={template?.language || "fr"}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    {TEMPLATE_LANGUAGES.map((lang) => (
                      <SelectItem key={lang.value} value={lang.value}>
                        {lang.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state.errors?.language && (
                  <p className="text-sm text-destructive mt-1">{state.errors.language[0]}</p>
                )}
              </div>

              <div>
                <Label htmlFor="service_id">Linked Service (Optional)</Label>
                <Select name="service_id" defaultValue={template?.service_id || "none"}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a service" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No service</SelectItem>
                    {services.map((service) => (
                      <SelectItem key={service.id} value={service.id}>
                        {service.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state.errors?.service_id && (
                  <p className="text-sm text-destructive mt-1">{state.errors.service_id[0]}</p>
                )}
              </div>
            </div>

            {/* Tags */}
            <div>
              <Label>Tags</Label>
              <div className="flex flex-wrap gap-2 mb-2">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
                  </Badge>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addTag())}
                />
                <Button type="button" onClick={addTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <input type="hidden" name="tags" value={JSON.stringify(tags)} />
            </div>

            {/* Settings */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Switch id="is_active" checked={isActive} onCheckedChange={setIsActive} />
                <Label htmlFor="is_active">Active Template</Label>
                <input type="hidden" name="is_active" value={isActive.toString()} />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content Editor with Drag & Drop Tokens */}
        <DraggableTokenEditor
          initialContent={content}
          onChange={setContent}
          availableTokens={availableTokens}
          placeholder="Enter your template content here. Drag tokens from the sidebar or click + to insert them."
        />
        <input type="hidden" name="content" value={content} />
        {state.errors?.content && (
          <p className="text-sm text-destructive mt-1">{state.errors.content[0]}</p>
        )}

        {/* Preview Mode */}
        {previewMode === "preview" && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Template Preview</CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setPreviewMode("editor")}
                >
                  <Code className="h-4 w-4 mr-1" />
                  Back to Editor
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div
                className="min-h-[300px] p-4 border rounded-md bg-background prose max-w-none"
                dangerouslySetInnerHTML={{ __html: content }}
              />
            </CardContent>
          </Card>
        )}

        {/* Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant={previewMode === "editor" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPreviewMode("editor")}
                >
                  <Code className="h-4 w-4 mr-1" />
                  Editor
                </Button>
                <Button
                  type="button"
                  variant={previewMode === "preview" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPreviewMode("preview")}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  Preview
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Button type="button" variant="outline">
                  Cancel
                </Button>
                <Button type="submit">
                  {mode === "create" ? "Create Template" : "Update Template"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </form>
  );
}
