"use client";

import { useState } from "react";
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Eye, Edit, Copy, Trash2, MoreHorizontal, FileText, Calendar, User } from "lucide-react";
import { DocumentTemplateWithMetadata } from "../lib/types";
import { deleteTemplateAction } from "../actions/delete";
import { duplicateTemplate } from "../actions/generate";

interface TemplateTableProps {
  templates: DocumentTemplateWithMetadata[];
  onTemplateDeleted?: () => void;
  onTemplateDuplicated?: () => void;
}

export function TemplateTable({
  templates,
  onTemplateDeleted,
  onTemplateDuplicated,
}: TemplateTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<DocumentTemplateWithMetadata | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState<string | null>(null);

  const handleDeleteClick = (template: DocumentTemplateWithMetadata) => {
    setTemplateToDelete(template);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!templateToDelete) return;

    setIsDeleting(true);
    try {
      const result = await deleteTemplateAction(templateToDelete.id);
      if (result.success) {
        setDeleteDialogOpen(false);
        setTemplateToDelete(null);
        onTemplateDeleted?.();
      } else {
        console.error("Failed to delete template:", result.error);
      }
    } catch (error) {
      console.error("Error deleting template:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDuplicate = async (template: DocumentTemplateWithMetadata) => {
    setIsDuplicating(template.id);
    try {
      const newName = `${template.name} (Copy)`;
      const result = await duplicateTemplate(template.id, newName);
      if (result.success) {
        onTemplateDuplicated?.();
      } else {
        console.error("Failed to duplicate template:", result.error);
      }
    } catch (error) {
      console.error("Error duplicating template:", error);
    } finally {
      setIsDuplicating(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      legal: "bg-blue-100 text-blue-800",
      administrative: "bg-green-100 text-green-800",
      communication: "bg-purple-100 text-purple-800",
      report: "bg-orange-100 text-orange-800",
      form: "bg-yellow-100 text-yellow-800",
      contract: "bg-red-100 text-red-800",
      other: "bg-gray-100 text-gray-800",
    };
    return colors[category] || colors.other;
  };

  if (templates.length === 0) {
    return (
      <div className="text-center py-12">
        <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium text-muted-foreground mb-2">No templates found</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Get started by creating your first document template.
        </p>
        <Button asChild>
          <Link href="./create">Create Template</Link>
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Updated</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {templates.map((template) => (
              <TableRow key={template.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{template.name}</div>
                    {(template.metadata as any)?.description && (
                      <div className="text-sm text-muted-foreground line-clamp-1">
                        {(template.metadata as any).description}
                      </div>
                    )}
                    {(template.metadata as any)?.tags && (template.metadata as any).tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-1">
                        {(template.metadata as any).tags.slice(0, 3).map((tag: string) => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                        {(template.metadata as any).tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{(template.metadata as any).tags.length - 3}
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge className={getCategoryColor("contract")}>
                    {/* {template.category.charAt(0).toUpperCase() + template.category.slice(1)} */}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant={template.is_active ? "default" : "secondary"}>
                    {template.is_active ? "Active" : "Inactive"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    {template.created_at && formatDate(template.created_at)}
                  </div>
                  {template.created_by_name && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <User className="h-3 w-3" />
                      {template.created_by_name}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Calendar className="h-3 w-3" />
                    {template.updated_at && formatDate(template.updated_at)}
                  </div>
                  {template.updated_by_name && (
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <User className="h-3 w-3" />
                      {template.updated_by_name}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`./${template.id}/view`}>
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`./${template.id}/edit`}>
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleDuplicate(template)}
                        disabled={isDuplicating === template.id}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        {isDuplicating === template.id ? "Duplicating..." : "Duplicate"}
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteClick(template)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Template</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{templateToDelete?.name}"? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
