"use client";

import React, { useCallback, useRef } from "react";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Link from "@tiptap/extension-link";
import Placeholder from "@tiptap/extension-placeholder";
import { Extension } from "@tiptap/core";
import { Plugin, PluginKey } from "@tiptap/pm/state";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { GripVertical, Plus } from "lucide-react";
import { TemplateTokenGroup } from "../lib/types";
import { cn } from "@/lib/utils";

interface DraggableTokenEditorProps {
  initialContent?: string;
  onChange?: (content: string) => void;
  placeholder?: string;
  availableTokens?: TemplateTokenGroup[];
  height?: string;
  className?: string;
}

// Custom TipTap extension for handling token drops
const TokenDropExtension = Extension.create({
  name: "tokenDrop",

  addProseMirrorPlugins() {
    return [
      new Plugin({
        key: new PluginKey("tokenDrop"),
        props: {
          handleDrop: (view, event, slice, moved) => {
            const tokenData = event.dataTransfer?.getData("application/token");
            if (tokenData) {
              event.preventDefault();
              const token = JSON.parse(tokenData);
              const tokenText = `{{ ${token.key} }}`;

              // Get the position where the drop occurred
              const pos = view.posAtCoords({
                left: event.clientX,
                top: event.clientY,
              });

              if (pos) {
                // Insert the token at the drop position
                const tr = view.state.tr.insertText(tokenText, pos.pos);
                view.dispatch(tr);
                return true;
              }
            }
            return false;
          },
        },
      }),
    ];
  },
});

// Draggable token component
interface DraggableTokenProps {
  token: {
    key: string;
    label: string;
    type: string;
    description?: string;
  };
  onInsert: (tokenKey: string) => void;
}

function DraggableToken({ token, onInsert }: DraggableTokenProps) {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("application/token", JSON.stringify(token));
    e.dataTransfer.effectAllowed = "copy";
  };

  const getTokenTypeColor = (type: string) => {
    switch (type) {
      case "text":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "date":
        return "bg-green-100 text-green-800 border-green-200";
      case "number":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "boolean":
        return "bg-orange-100 text-orange-800 border-orange-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      className="group cursor-move border rounded-lg p-3 hover:shadow-md transition-all duration-200 bg-white hover:bg-gray-50"
    >
      <div className="flex items-start justify-between gap-2">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <GripVertical className="h-3 w-3 text-gray-400 group-hover:text-gray-600" />
            <span className="font-medium text-sm truncate">{token.label}</span>
          </div>
          <div className="flex items-center gap-2 mb-2">
            <Badge variant="outline" className={cn("text-xs", getTokenTypeColor(token.type))}>
              {token.type}
            </Badge>
            <code className="text-xs text-gray-500 bg-gray-100 px-1 rounded">
              {`{{ ${token.key} }}`}
            </code>
          </div>
          {token.description && (
            <p className="text-xs text-gray-600 line-clamp-2">{token.description}</p>
          )}
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={() => onInsert(token.key)}
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}

export function DraggableTokenEditor({
  initialContent = "",
  onChange,
  placeholder = "Start writing your template...",
  availableTokens = [],
  height = "400px",
  className = "",
}: DraggableTokenEditorProps) {
  const editorRef = useRef<any>(null);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: "text-primary underline underline-offset-2",
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      TokenDropExtension,
    ],
    content: initialContent,
    onUpdate: ({ editor }) => {
      onChange?.(editor.getHTML());
    },
  });

  const insertToken = useCallback(
    (tokenKey: string) => {
      if (editor) {
        const tokenText = `{{ ${tokenKey} }}`;
        editor.chain().focus().insertContent(tokenText).run();
      }
    },
    [editor]
  );

  const handleEditorDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
  };

  return (
    <div className={cn("grid grid-cols-1 lg:grid-cols-3 gap-6", className)}>
      {/* Editor */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Template Content</CardTitle>
            <p className="text-sm text-muted-foreground">
              Drag tokens from the sidebar or click the + button to insert them
            </p>
          </CardHeader>
          <CardContent>
            <div
              ref={editorRef}
              onDragOver={handleEditorDragOver}
              className="border rounded-md min-h-[400px] relative"
              style={{ height }}
            >
              <RichTextEditor
                initialContent={initialContent}
                onChange={onChange}
                placeholder={placeholder}
                height={height}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Token Sidebar */}
      <div>
        <Card className="sticky top-4">
          <CardHeader>
            <CardTitle className="text-lg">Available Tokens</CardTitle>
            <p className="text-sm text-muted-foreground">
              Drag tokens into the editor or click + to insert
            </p>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs
              defaultValue={availableTokens[0]?.name.toLowerCase().replace(/\s+/g, "-")}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2 mx-4 mb-4">
                {availableTokens.slice(0, 2).map((group) => (
                  <TabsTrigger
                    key={group.name}
                    value={group.name.toLowerCase().replace(/\s+/g, "-")}
                    className="text-xs"
                  >
                    {group.name.split(" ")[0]}
                  </TabsTrigger>
                ))}
              </TabsList>

              {availableTokens.map((group) => (
                <TabsContent
                  key={group.name}
                  value={group.name.toLowerCase().replace(/\s+/g, "-")}
                  className="space-y-3 px-4 pb-4 max-h-[500px] overflow-y-auto"
                >
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">{group.name}</h4>
                    <p className="text-xs text-muted-foreground">{group.description}</p>
                  </div>

                  <div className="space-y-2">
                    {group.tokens.map((token) => (
                      <DraggableToken key={token.key} token={token} onInsert={insertToken} />
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
