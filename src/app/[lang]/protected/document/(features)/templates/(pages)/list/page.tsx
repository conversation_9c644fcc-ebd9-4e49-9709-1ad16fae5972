"use server";
import { Suspense } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Plus, FileText } from "lucide-react";
import { Pagination } from "@/components/Pagination";
import { listTemplates } from "../../actions/list";
import { TemplateTable } from "../../components/TemplateTable";
import { TemplateFilters } from "../../components/TemplateFilters";

interface TemplateListPageProps {
  searchParams: Promise<{
    search?: string;
    category?: string;
    status?: string;
    tags?: string;
    page?: string;
  }>;
}

export default async function TemplateListPage({
  searchParams: searchParamsPromise,
}: TemplateListPageProps) {
  const searchParams = await searchParamsPromise;
  const result = await listTemplates({
    search: searchParams.search,
    category: searchParams.category as any,
    is_active: searchParams.status ? searchParams.status === "true" : undefined,
    tags: searchParams.tags ? searchParams.tags.split(",") : undefined,
    page: parseInt(searchParams.page || "1"),
    limit: 10,
  });

  if (!result.success) {
    throw new Error(result.message);
  }

  if (!result.data) {
    throw new Error("No data returned");
  }

  const { templates, pagination } = result.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Document Templates</h1>
          <p className="text-muted-foreground">Manage and organize your document templates</p>
        </div>
        <Button asChild>
          <Link href="./create">
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Templates</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <FileText className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{templates.filter((t) => t.is_active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive</CardTitle>
            <FileText className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{templates.filter((t) => !t.is_active).length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <FileText className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {/* {new Set(templates.map((t) => t.category)).size} */}
              Category
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <TemplateFilters />

      {/* Templates Table */}
      <Card>
        <CardHeader>
          <CardTitle>Templates ({pagination.total})</CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div className="p-6">Loading templates...</div>}>
            <TemplateTable templates={templates} />
          </Suspense>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Pagination
          currentPage={pagination.page}
          totalItems={pagination.totalPages}
          basePath="/protected/document/templates/list"
          pageSize={pagination.limit}
          lang={""}
        />
      )}
    </div>
  );
}
