import { Suspense } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { TemplateForm } from "../../components/TemplateForm";
import { getAvailableTokens } from "../../actions/view";

export default async function CreateTemplatePage() {
  // Get available tokens for the form
  const tokensResult = await getAvailableTokens();
  const availableTokens = tokensResult.success ? tokensResult.data : [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" asChild>
          <Link href="../list">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Templates
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Template</h1>
          <p className="text-muted-foreground">
            Create a new document template with WYSIWYG editor and token support
          </p>
        </div>
      </div>

      {/* Template Form */}
      <Suspense fallback={<div className="p-6">Loading form...</div>}>
        <TemplateForm mode="create" availableTokens={availableTokens} />
      </Suspense>
    </div>
  );
}
