import { Database } from "@/lib/types/database.types";

// Database types
export type DocumentTemplate = Database["public"]["Tables"]["document_templates"]["Row"];
export type DocumentTemplateInsert = Database["public"]["Tables"]["document_templates"]["Insert"];
export type DocumentTemplateUpdate = Database["public"]["Tables"]["document_templates"]["Update"];

// Extended types for UI
export interface DocumentTemplateWithMetadata extends DocumentTemplate {
  created_by_name?: string;
  updated_by_name?: string;
  usage_count?: number;
}

// Form types
export interface CreateDocumentTemplateData {
  name: string;
  description?: string;
  content: string;
  category: string;
  tags?: string[];
  is_active?: boolean;
}

export interface UpdateDocumentTemplateData {
  name?: string;
  description?: string;
  content?: string;
  category?: string;
  tags?: string[];
  is_active?: boolean;
}

// Filter and search types
export interface DocumentTemplateFilters {
  category?: string;
  is_active?: boolean;
  search?: string;
  tags?: string[];
  created_by?: string;
  page?: number;
  limit?: number;
}

// Template generation types
export interface TemplateToken {
  key: string;
  label: string;
  description?: string;
  type: "text" | "date" | "number" | "boolean" | "list";
  required?: boolean;
  default_value?: any;
}

export interface TemplateTokenGroup {
  name: string;
  description?: string;
  tokens: TemplateToken[];
}

export interface GenerateDocumentData {
  template_id: string;
  tokens: Record<string, any>;
  output_format?: "html" | "pdf" | "docx";
}

// Service response types
export interface DocumentTemplateListResponse {
  templates: DocumentTemplateWithMetadata[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Template categories
export const TEMPLATE_CATEGORIES = [
  "legal",
  "administrative",
  "communication",
  "report",
  "form",
  "contract",
  "other",
] as const;

export type TemplateCategory = (typeof TEMPLATE_CATEGORIES)[number];

// Template status
export const TEMPLATE_STATUS = {
  ACTIVE: true,
  INACTIVE: false,
} as const;

// Available token types for template content
export const AVAILABLE_TOKEN_GROUPS: TemplateTokenGroup[] = [
  {
    name: "Contact Information",
    description: "Basic contact details",
    tokens: [
      { key: "contact.full_name", label: "Full Name", type: "text", required: true },
      { key: "contact.first_name", label: "First Name", type: "text" },
      { key: "contact.last_name", label: "Last Name", type: "text" },
      { key: "contact.email", label: "Email", type: "text" },
      { key: "contact.phone", label: "Phone", type: "text" },
      { key: "contact.address", label: "Address", type: "text" },
      { key: "contact.date_of_birth", label: "Date of Birth", type: "date" },
    ],
  },
  {
    name: "Organization Information",
    description: "Organization and employee details",
    tokens: [
      { key: "organization.name", label: "Organization Name", type: "text" },
      { key: "organization.address", label: "Organization Address", type: "text" },
      { key: "employee.name", label: "Employee Name", type: "text" },
      { key: "employee.title", label: "Employee Title", type: "text" },
      { key: "employee.email", label: "Employee Email", type: "text" },
    ],
  },
  {
    name: "Document Information",
    description: "Document metadata and dates",
    tokens: [
      {
        key: "document.date",
        label: "Document Date",
        type: "date",
        default_value: new Date().toISOString(),
      },
      { key: "document.reference", label: "Reference Number", type: "text" },
      { key: "document.title", label: "Document Title", type: "text" },
    ],
  },
  {
    name: "Custom Fields",
    description: "Customizable content fields",
    tokens: [
      { key: "custom.field1", label: "Custom Field 1", type: "text" },
      { key: "custom.field2", label: "Custom Field 2", type: "text" },
      { key: "custom.field3", label: "Custom Field 3", type: "text" },
      { key: "custom.notes", label: "Notes", type: "text" },
    ],
  },
];
