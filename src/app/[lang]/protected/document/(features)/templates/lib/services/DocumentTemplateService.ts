import { createClient } from "@/lib/supabase/server";
import {
  DocumentTemplate,
  DocumentTemplateInsert,
  DocumentTemplateUpdate,
  DocumentTemplateWithMetadata,
  DocumentTemplateFilters,
  DocumentTemplateListResponse,
  GenerateDocumentData,
  AVAILABLE_TOKEN_GROUPS,
} from "../types";
import {
  ServiceResponse,
  successResponse,
  errorResponse,
} from "@/lib/types/responses/serviceResponse";

export class DocumentTemplateService {
  /**
   * Create a new document template
   */
  async createTemplate(data: DocumentTemplateInsert): Promise<ServiceResponse<DocumentTemplate>> {
    try {
      const supabase = await createClient();
      const { data: template, error } = await supabase
        .from("document_templates")
        .insert(data)
        .select()
        .single();

      if (error) {
        return errorResponse("Failed to create template", error.message);
      }

      return successResponse(template, "Template created successfully");
    } catch (error) {
      return errorResponse(
        "Failed to create template",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Get template by ID
   */
  async getTemplateById(id: string): Promise<ServiceResponse<DocumentTemplateWithMetadata>> {
    try {
      const supabase = await createClient();
      const { data: template, error } = await supabase
        .from("document_templates")
        .select(
          `
          *`
          //   created_by_profile:user_profiles!document_templates_created_by_fkey(full_name),
          //   updated_by_profile:user_profiles!document_templates_updated_by_fkey(full_name)
          // `
        )
        .eq("id", id)
        .single();

      if (error) {
        return errorResponse("Template not found", error.message);
      }

      // Transform the response to include metadata
      const templateWithMetadata: DocumentTemplateWithMetadata = {
        ...template,
        created_by_name: "", //template.created_by_profile?.full_name,
        updated_by_name: "", //template.updated_by_profile?.full_name,
      };

      return successResponse(templateWithMetadata, "Template fetched successfully");
    } catch (error) {
      return errorResponse(
        "Failed to fetch template",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * List templates with filtering and pagination
   */
  async listTemplates(
    organizationId: string,
    filters: DocumentTemplateFilters = {}
  ): Promise<ServiceResponse<DocumentTemplateListResponse>> {
    try {
      const supabase = await createClient();
      const { category, is_active, search, tags, created_by, page = 1, limit = 10 } = filters;

      const query = supabase
        .from("document_templates")
        .select(
          `
          *`,

          //   created_by_profile:user_profiles!document_templates_created_by_fkey(full_name),
          //   updated_by_profile:user_profiles!document_templates_updated_by_fkey(full_name)
          // `,
          { count: "exact" }
        )
        .eq("organization_id", organizationId)
        .order("updated_at", { ascending: false });

      // Apply filters
      // if (category) {
      //   query = query.eq("category", category);
      // }

      // if (typeof is_active === "boolean") {
      //   query = query.eq("is_active", is_active);
      // }

      // if (search) {
      //   query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
      // }

      // if (tags && tags.length > 0) {
      //   query = query.overlaps("tags", tags);
      // }

      // if (created_by) {
      //   query = query.eq("created_by", created_by);
      // }

      // Apply pagination
      // const offset = (page - 1) * limit;
      // query = query.range(offset, offset + limit - 1);

      const { data: templates, error, count } = await query;

      if (error) {
        return errorResponse("Failed to fetch templates", error.message);
      }

      // Transform the response to include metadata
      const templatesWithMetadata: DocumentTemplateWithMetadata[] = (templates || []).map(
        (template) => ({
          ...template,
          // created_by_name: template.created_by_profile?.full_name,
          // updated_by_name: template.updated_by_profile?.full_name,
        })
      );

      const totalPages = Math.ceil((count || 0) / limit);

      const response: DocumentTemplateListResponse = {
        templates: templatesWithMetadata,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages,
        },
      };

      return successResponse(response, "Templates fetched successfully");
    } catch (error) {
      return errorResponse(
        "Failed to fetch templates",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Update template
   */
  async updateTemplate(
    id: string,
    data: DocumentTemplateUpdate
  ): Promise<ServiceResponse<DocumentTemplate>> {
    try {
      const supabase = await createClient();
      const { data: template, error } = await supabase
        .from("document_templates")
        .update({
          ...data,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        return errorResponse("Failed to update template", error.message);
      }

      return successResponse(template, "Template updated successfully");
    } catch (error) {
      return errorResponse(
        "Failed to update template",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Delete template
   */
  async deleteTemplate(id: string): Promise<ServiceResponse<void>> {
    try {
      const supabase = await createClient();
      const { error } = await supabase.from("document_templates").delete().eq("id", id);

      if (error) {
        return errorResponse("Failed to delete template", error.message);
      }

      return successResponse(undefined, "Template deleted successfully");
    } catch (error) {
      return errorResponse(
        "Failed to delete template",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Duplicate template
   */
  async duplicateTemplate(
    id: string,
    newName: string,
    organizationId: string,
    userId: string
  ): Promise<ServiceResponse<DocumentTemplate>> {
    try {
      // Get the original template
      const originalResult = await this.getTemplateById(id);
      if (!originalResult.success || !originalResult.data) {
        return errorResponse("Original template not found", "Template to duplicate not found");
      }

      const original = originalResult.data;

      // Create the duplicate
      const duplicateData: DocumentTemplateInsert = {
        name: newName,
        // description: `Copy of ${original.description || original.name}`,
        content: original.content,
        // category: original.category,
        // tags: original.tags,
        is_active: false, // Start as inactive
        organization_id: organizationId,
        created_by: userId,
        template_type: "",
      };

      return await this.createTemplate(duplicateData);
    } catch (error) {
      return errorResponse(
        "Failed to duplicate template",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  /**
   * Generate document from template
   */
  async generateDocument(
    data: GenerateDocumentData
  ): Promise<ServiceResponse<{ content: string; format: string }>> {
    try {
      // Get the template
      const templateResult = await this.getTemplateById(data.template_id);
      if (!templateResult.success || !templateResult.data) {
        return errorResponse("Template not found", "Template not found");
      }

      const template = templateResult.data;
      let content = template.content;

      // Replace tokens in the content
      Object.entries(data.tokens).forEach(([key, value]) => {
        const tokenPattern = new RegExp(`{{\\s*${key}\\s*}}`, "g");
        content = content.replace(tokenPattern, String(value || ""));
      });

      // Handle any remaining unreplaced tokens (optional: remove or leave as is)
      // For now, we'll leave them as is for debugging purposes

      return successResponse(
        {
          content,
          format: data.output_format || "html",
        },
        "Document generated successfully"
      );
    } catch (error) {
      return errorResponse(
        "Failed to generate document",
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }



  /**
   * Get available tokens for template editing
   */
  getAvailableTokens() {
    return AVAILABLE_TOKEN_GROUPS;
  }

  /**
   * Validate template content for token syntax
   */
  validateTemplateContent(content: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for malformed tokens (e.g., unclosed braces)
    const tokenPattern = /{{[^}]*}}/g;
    const matches = content.match(tokenPattern) || [];

    matches.forEach((match) => {
      // Check if token has proper format
      const tokenContent = match.slice(2, -2).trim();
      if (!tokenContent) {
        errors.push(`Empty token found: ${match}`);
      }

      // Check for nested braces
      if (tokenContent.includes("{") || tokenContent.includes("}")) {
        errors.push(`Invalid token format (nested braces): ${match}`);
      }
    });

    // Check for unclosed tokens
    const openBraces = (content.match(/{/g) || []).length;
    const closeBraces = (content.match(/}/g) || []).length;

    if (openBraces !== closeBraces) {
      errors.push("Mismatched braces in template content");
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
