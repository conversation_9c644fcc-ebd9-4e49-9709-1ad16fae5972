"use server";

import { z } from "zod";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { createClient } from "@/lib/supabase/server";

// Validation schema
const generateDocumentSchema = z.object({
  template_id: z.string().min(1, "Template ID is required"),
  tokens: z.record(z.any()),
  output_format: z.enum(["html", "pdf", "docx"]).optional().default("html"),
});

/**
 * Generate a document from a template
 */
export async function generateDocument(formData: FormData) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Parse and validate form data
    const rawData = {
      template_id: formData.get("template_id"),
      tokens: formData.get("tokens") ? JSON.parse(formData.get("tokens") as string) : {},
      output_format: formData.get("output_format") || "html",
    };

    const validatedData = generateDocumentSchema.parse(rawData);

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const templateResult = await templateService.getTemplateById(validatedData.template_id);

    if (!templateResult.success) {
      return { success: false, error: "Template not found" };
    }

    if (templateResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Template not found" };
    }

    // Generate document using service
    const result = await templateService.generateDocument(validatedData);

    if (!result.success) {
      return { success: false, error: result.message };
    }

    return {
      success: true,
      data: result.data,
      message: "Document generated successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = Object.entries(error.flatten().fieldErrors)
        .map(([field, messages]) => `${field}: ${messages?.join(", ")}`)
        .join("; ");
      return { success: false, error: `Validation failed: ${errorMessages}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to generate document",
    };
  }
}

/**
 * Duplicate a template
 */
export async function duplicateTemplate(templateId: string, newName: string) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const templateResult = await templateService.getTemplateById(templateId);

    if (!templateResult.success) {
      return { success: false, error: "Template not found" };
    }

    if (templateResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Template not found" };
    }

    // Duplicate template using service
    const result = await templateService.duplicateTemplate(
      templateId,
      newName,
      userProfile.organizationId,
      userProfile.id
    );

    if (!result.success) {
      return { success: false, error: result.message };
    }

    return {
      success: true,
      data: result.data,
      message: "Template duplicated successfully",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to duplicate template",
    };
  }
}

/**
 * Validate template content
 */
export async function validateTemplateContent(content: string) {
  try {
    const templateService = new DocumentTemplateService();
    const validation = templateService.validateTemplateContent(content);

    return {
      success: true,
      data: validation,
      message: validation.isValid ? "Template content is valid" : "Template content has errors",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to validate template content",
    };
  }
}
