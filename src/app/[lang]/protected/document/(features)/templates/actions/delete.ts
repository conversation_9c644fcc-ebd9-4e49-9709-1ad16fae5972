"use server";

import { revalidatePath } from "next/cache";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { redirect } from "next/navigation";

/**
 * Delete a document template (with redirect)
 */
export async function deleteTemplate(id: string) {
  const userProfile = await auth.getCurrentUserProfile();
  let shouldRedirect = false;
  let errorMessage = "";

  if (!userProfile?.organizationId) {
    throw new Error("Organization context required");
  }

  try {
    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);

    if (!existingResult.success) {
      errorMessage = "Template not found";
    } else if (existingResult.data?.organization_id !== userProfile.organizationId) {
      errorMessage = "Template not found";
    } else {
      // Delete template using service
      const result = await templateService.deleteTemplate(id);

      if (!result.success) {
        errorMessage = result.message;
      } else {
        // Success - prepare for redirect
        revalidatePath("/protected/document/templates");
        shouldRedirect = true;
      }
    }

  } catch (error) {
    errorMessage = error instanceof Error ? error.message : "Failed to delete template";
  }

  // Handle errors
  if (errorMessage) {
    throw new Error(errorMessage);
  }

  // Redirect only on success - completely outside try-catch
  if (shouldRedirect) {
    redirect("/protected/document/templates/list");
  }
}

/**
 * Delete a document template (without redirect)
 */
export async function deleteTemplateAction(id: string) {
  try {
    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);

    if (!existingResult.success) {
      return { success: false, error: "Template not found" };
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Template not found" };
    }

    // Delete template using service
    const result = await templateService.deleteTemplate(id);

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate the templates list
    revalidatePath("/protected/document/templates");

    return {
      success: true,
      message: "Template deleted successfully",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete template",
    };
  }
}
