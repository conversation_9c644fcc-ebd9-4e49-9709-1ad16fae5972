"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { TEMPLATE_TYPES } from "../lib/types";
import { redirect } from "next/navigation";

// Validation schema
const updateTemplateSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long").optional(),
  description: z.string().optional(),
  content: z.string().min(1, "Content is required").optional(),
  template_type: z.enum(TEMPLATE_TYPES).optional(),
  language: z.enum(["fr", "en"]).optional(),
  service_id: z.string().optional(),
  tags: z.array(z.string()).optional(),
  is_active: z.boolean().optional(),
  signature_required: z.boolean().optional(),
});

export type UpdateTemplateFormState = {
  success: boolean;
  message: string;
  errors?: Record<string, string[] | undefined>;
};

/**
 * Update a document template (with redirect)
 */
export async function updateTemplate(
  id: string,
  prevState: UpdateTemplateFormState,
  formData: FormData
): Promise<UpdateTemplateFormState> {
  const userProfile = await auth.getCurrentUserProfile();
  let shouldRedirect = false;

  if (!userProfile?.organizationId) {
    return {
      success: false,
      message: "Organization context required",
    };
  }

  let response: UpdateTemplateFormState;

  try {
    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);

    if (!existingResult.success) {
      response = {
        success: false,
        message: "Template not found",
      };
    } else if (existingResult.data?.organization_id !== userProfile.organizationId) {
      response = {
        success: false,
        message: "Template not found",
      };
    } else {
      // Parse and validate form data
      const rawData = {
        name: formData.get("name") || undefined,
        content: formData.get("content") || undefined,
        template_type: formData.get("template_type") || undefined,
        language: formData.get("language") || undefined,
        service_id: formData.get("service_id") || undefined,
        is_active: formData.get("is_active") ? formData.get("is_active") === "true" : undefined,
        signature_required: formData.get("signature_required") ? formData.get("signature_required") === "true" : undefined,
        // Metadata fields
        description: formData.get("description") || undefined,
        tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : undefined,
      };

      // Remove undefined values
      const cleanedData = Object.fromEntries(
        Object.entries(rawData).filter(([_, value]) => value !== undefined)
      );

      const validatedData = updateTemplateSchema.parse(cleanedData);

      // Validate template content if provided
      if (validatedData.content) {
        const validation = templateService.validateTemplateContent(validatedData.content);

        if (!validation.isValid) {
          response = {
            success: false,
            message: "Template content validation failed",
            errors: { content: validation.errors },
          };
        } else {
          // Update template using service
          const updateData: any = { ...validatedData };

          // Handle metadata fields
          if (validatedData.description !== undefined || validatedData.tags !== undefined) {
            const currentMetadata = (existingResult.data?.metadata as any) || {};
            updateData.metadata = {
              ...currentMetadata,
              ...(validatedData.description !== undefined && {
                description: validatedData.description,
              }),
              ...(validatedData.tags !== undefined && { tags: validatedData.tags }),
            };
            delete updateData.description;
            delete updateData.tags;
          }

          // Handle service_id
          if (updateData.service_id === "none") {
            updateData.service_id = null;
          }

          const result = await templateService.updateTemplate(id, updateData);

          if (!result.success) {
            response = {
              success: false,
              message: result.message,
            };
          } else {
            // Success - prepare for redirect
            revalidatePath("/protected/document/templates");
            revalidatePath(`/protected/document/templates/${id}`);
            shouldRedirect = true;
            response = {
              success: true,
              message: "Template updated successfully",
            };
          }
        }
      } else {
        // No content validation needed, proceed with update
        const updateData: any = { ...validatedData };

        // Handle metadata fields
        if (validatedData.description !== undefined || validatedData.tags !== undefined) {
          const currentMetadata = (existingResult.data?.metadata as any) || {};
          updateData.metadata = {
            ...currentMetadata,
            ...(validatedData.description !== undefined && {
              description: validatedData.description,
            }),
            ...(validatedData.tags !== undefined && { tags: validatedData.tags }),
          };
          delete updateData.description;
          delete updateData.tags;
        }

        // Handle service_id
        if (updateData.service_id === "none") {
          updateData.service_id = null;
        }

        const result = await templateService.updateTemplate(id, updateData);

        if (!result.success) {
          response = {
            success: false,
            message: result.message,
          };
        } else {
          // Success - prepare for redirect
          revalidatePath("/protected/document/templates");
          revalidatePath(`/protected/document/templates/${id}`);
          shouldRedirect = true;
          response = {
            success: true,
            message: "Template updated successfully",
          };
        }
      }
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      response = {
        success: false,
        message: "Validation failed",
        errors: error.flatten().fieldErrors,
      };
    } else {
      response = {
        success: false,
        message: error instanceof Error ? error.message : "Failed to update template",
      };
    }
  }

  // Redirect only on success - completely outside try-catch
  if (shouldRedirect) {
    redirect(`/protected/document/templates/${id}/view`);
  }

  return response;
}

/**
 * Update a document template (without redirect)
 */
export async function updateTemplateAction(id: string, formData: FormData) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);

    if (!existingResult.success) {
      return { success: false, error: "Template not found" };
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Template not found" };
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name") || undefined,
      description: formData.get("description") || undefined,
      content: formData.get("content") || undefined,
      // category: formData.get("category") || undefined,
      // tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : undefined,
      is_active: formData.get("is_active") ? formData.get("is_active") === "true" : undefined,
    };

    // Remove undefined values
    const cleanedData = Object.fromEntries(
      Object.entries(rawData).filter(([_, value]) => value !== undefined)
    );

    const validatedData = updateTemplateSchema.parse(cleanedData);

    // Validate template content if provided
    if (validatedData.content) {
      const validation = templateService.validateTemplateContent(validatedData.content);

      if (!validation.isValid) {
        return {
          success: false,
          error: "Template content validation failed: " + validation.errors.join(", "),
        };
      }
    }

    // Update template using service
    const result = await templateService.updateTemplate(id, {
      ...validatedData,
    });

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate the templates
    revalidatePath("/protected/document/templates");
    revalidatePath(`/protected/document/templates/${id}`);

    return {
      success: true,
      data: result.data,
      message: "Template updated successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = Object.entries(error.flatten().fieldErrors)
        .map(([field, messages]) => `${field}: ${messages?.join(", ")}`)
        .join("; ");
      return { success: false, error: `Validation failed: ${errorMessages}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update template",
    };
  }
}
