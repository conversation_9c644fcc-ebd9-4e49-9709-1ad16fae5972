"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
// import { TEMPLATE_CATEGORIES } from "../lib/types";
import { redirect } from "next/navigation";

// Validation schema
const updateTemplateSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long").optional(),
  description: z.string().optional(),
  content: z.string().min(1, "Content is required").optional(),
  // category: z.enum(TEMPLATE_CATEGORIES).optional(),
  // tags: z.array(z.string()).optional(),
  is_active: z.boolean().optional(),
});

export type UpdateTemplateFormState = {
  success: boolean;
  message: string;
  errors?: Record<string, string[] | undefined>;
};

/**
 * Update a document template (with redirect)
 */
export async function updateTemplate(
  id: string,
  prevState: UpdateTemplateFormState,
  formData: FormData
): Promise<UpdateTemplateFormState> {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);

    if (!existingResult.success) {
      throw new Error("Template not found");
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      throw new Error("Template not found");
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name") || undefined,
      description: formData.get("description") || undefined,
      content: formData.get("content") || undefined,
      // category: formData.get("category") || undefined,
      // tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : undefined,
      is_active: formData.get("is_active") ? formData.get("is_active") === "true" : undefined,
    };

    // Remove undefined values
    const cleanedData = Object.fromEntries(
      Object.entries(rawData).filter(([_, value]) => value !== undefined)
    );

    const validatedData = updateTemplateSchema.parse(cleanedData);

    // Validate template content if provided
    if (validatedData.content) {
      const validation = templateService.validateTemplateContent(validatedData.content);

      if (!validation.isValid) {
        return {
          success: false,
          message: "Template content validation failed",
          errors: { content: validation.errors },
        };
      }
    }

    // Update template using service
    const result = await templateService.updateTemplate(id, {
      ...validatedData,
    });

    if (!result.success) {
      return {
        success: false,
        message: result.message,
      };
    }

    // Revalidate and redirect
    revalidatePath("/protected/document/templates");
    revalidatePath(`/protected/document/templates/${id}`);
    redirect(`/protected/document/templates/${id}/view`);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Validation failed",
        errors: error.flatten().fieldErrors,
      };
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to update template",
    };
  }
}

/**
 * Update a document template (without redirect)
 */
export async function updateTemplateAction(id: string, formData: FormData) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Verify template exists and belongs to organization
    const templateService = new DocumentTemplateService();
    const existingResult = await templateService.getTemplateById(id);

    if (!existingResult.success) {
      return { success: false, error: "Template not found" };
    }

    if (existingResult.data?.organization_id !== userProfile.organizationId) {
      return { success: false, error: "Template not found" };
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name") || undefined,
      description: formData.get("description") || undefined,
      content: formData.get("content") || undefined,
      // category: formData.get("category") || undefined,
      // tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : undefined,
      is_active: formData.get("is_active") ? formData.get("is_active") === "true" : undefined,
    };

    // Remove undefined values
    const cleanedData = Object.fromEntries(
      Object.entries(rawData).filter(([_, value]) => value !== undefined)
    );

    const validatedData = updateTemplateSchema.parse(cleanedData);

    // Validate template content if provided
    if (validatedData.content) {
      const validation = templateService.validateTemplateContent(validatedData.content);

      if (!validation.isValid) {
        return {
          success: false,
          error: "Template content validation failed: " + validation.errors.join(", "),
        };
      }
    }

    // Update template using service
    const result = await templateService.updateTemplate(id, {
      ...validatedData,
    });

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate the templates
    revalidatePath("/protected/document/templates");
    revalidatePath(`/protected/document/templates/${id}`);

    return {
      success: true,
      data: result.data,
      message: "Template updated successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = Object.entries(error.flatten().fieldErrors)
        .map(([field, messages]) => `${field}: ${messages?.join(", ")}`)
        .join("; ");
      return { success: false, error: `Validation failed: ${errorMessages}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update template",
    };
  }
}
