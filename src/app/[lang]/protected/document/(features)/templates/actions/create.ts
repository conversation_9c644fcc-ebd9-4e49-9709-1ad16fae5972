"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
// import { TEMPLATE_CATEGORIES } from "../lib/types";
import { redirect } from "next/navigation";

// Validation schema
const createTemplateSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().optional(),
  content: z.string().min(1, "Content is required"),
  // category: z.enum(TEMPLATE_CATEGORIES, { required_error: "Category is required" }),
  // tags: z.array(z.string()).optional(),
  is_active: z.boolean().optional().default(true),
});

export type CreateTemplateFormState = {
  success: boolean;
  message: string;
  errors?: Record<string, string[] | undefined>;
};

/**
 * Create a new document template (with redirect)
 */
export async function createTemplate(
  prevState: CreateTemplateFormState,
  formData: FormData
): Promise<CreateTemplateFormState> {
  let result;
  const userProfile = await auth.getCurrentUserProfile();
  try {
    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name"),
      description: formData.get("description") || undefined,
      content: formData.get("content"),
      // category: formData.get("category"),
      // tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : [],
      is_active: formData.get("is_active") === "true",
    };

    const validatedData = createTemplateSchema.parse(rawData);

    // Validate template content
    const templateService = new DocumentTemplateService();
    const validation = templateService.validateTemplateContent(validatedData.content);

    if (!validation.isValid) {
      return {
        success: false,
        message: "Template content validation failed",
        errors: { content: validation.errors },
      };
    }

    // Create template using service
    result = await templateService.createTemplate({
      name: validatedData.name,
      // description: validatedData.description || null,
      content: validatedData.content,
      // category: validatedData.category,
      // tags: validatedData.tags || [],
      is_active: validatedData.is_active,
      organization_id: userProfile.organizationId,
      created_by: userProfile.id,
      service_id: "41cf95cb-b744-4680-af35-909f5b6465fb",
      template_type: "service_agreement",
    });

    if (!result.success) {
      return {
        success: false,
        message: result.message,
      };
    }

    // Revalidate and redirect
    revalidatePath(`/${userProfile.language}/protected/document/templates`);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Validation failed",
        errors: error.flatten().fieldErrors,
      };
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to create template",
    };
  }

  redirect(`/${userProfile.language}/protected/document/templates/${result.data?.id}/view`);
}

/**
 * Create a new document template (without redirect)
 */
export async function createTemplateAction(formData: FormData) {
  try {
    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name"),
      description: formData.get("description") || undefined,
      content: formData.get("content"),
      // category: formData.get("category"),
      // tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : [],
      is_active: formData.get("is_active") === "true",
    };

    const validatedData = createTemplateSchema.parse(rawData);

    // Validate template content
    const templateService = new DocumentTemplateService();
    const validation = templateService.validateTemplateContent(validatedData.content);

    if (!validation.isValid) {
      return {
        success: false,
        error: "Template content validation failed: " + validation.errors.join(", "),
      };
    }

    // Create template using service
    const result = await templateService.createTemplate({
      name: validatedData.name,
      // description: validatedData.description || null,
      content: validatedData.content,
      // category: validatedData.category,
      // tags: validatedData.tags || [],
      is_active: validatedData.is_active,
      organization_id: userProfile.organizationId,
      created_by: userProfile.id,
      service_id: "41cf95cb-b744-4680-af35-909f5b6465fb",
      template_type: "service_agreement",
    });

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate the templates list
    revalidatePath("/protected/document/templates");

    return {
      success: true,
      data: result.data,
      message: "Template created successfully",
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = Object.entries(error.flatten().fieldErrors)
        .map(([field, messages]) => `${field}: ${messages?.join(", ")}`)
        .join("; ");
      return { success: false, error: `Validation failed: ${errorMessages}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create template",
    };
  }
}
