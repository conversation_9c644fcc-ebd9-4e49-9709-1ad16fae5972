"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { DocumentTemplateService } from "../lib/services/DocumentTemplateService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { TEMPLATE_TYPES } from "../lib/types";
import { redirect } from "next/navigation";

// Validation schema aligned with database schema
const createTemplateSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  content: z.string().min(1, "Content is required"),
  template_type: z.enum(TEMPLATE_TYPES, { required_error: "Template type is required" }),
  language: z.enum(["fr", "en"], { required_error: "Language is required" }),
  service_id: z.string().optional(),
  is_active: z.boolean().optional().default(true),
  signature_required: z.boolean().optional().default(false),
  // Metadata fields (stored in JSONB)
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

export type CreateTemplateFormState = {
  success: boolean;
  message: string;
  errors?: Record<string, string[] | undefined>;
};

/**
 * Create a new document template (with redirect)
 */
export async function createTemplate(
  prevState: CreateTemplateFormState,
  formData: FormData
): Promise<CreateTemplateFormState> {
  let result;
  const userProfile = await auth.getCurrentUserProfile();
  try {
    if (!userProfile?.organizationId) {
      throw new Error("Organization context required");
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name"),
      description: formData.get("description") || undefined,
      content: formData.get("content"),
      template_type: formData.get("template_type"),
      language: formData.get("language"),
      service_id: formData.get("service_id"),
      tags: JSON.parse((formData.get("tags") as string) || "[]"),
      is_active: formData.get("is_active") === "true",
      signature_required: formData.get("signature_required") === "true",
    };

    const validatedData = createTemplateSchema.parse(rawData);

    // Validate template content
    const templateService = new DocumentTemplateService();
    const validation = templateService.validateTemplateContent(validatedData.content);

    if (!validation.isValid) {
      return {
        success: false,
        message: "Template content validation failed",
        errors: { content: validation.errors },
      };
    }

    // Create template using service
    result = await templateService.createTemplate({
      name: validatedData.name,
      content: validatedData.content,
      template_type: validatedData.template_type,
      language: validatedData.language,
      service_id: validatedData.service_id && validatedData.service_id !== "none" ? validatedData.service_id : null,
      is_active: validatedData.is_active,
      signature_required: validatedData.signature_required,
      organization_id: userProfile.organizationId,
      created_by: userProfile.id,
      metadata: {
        tags: validatedData.tags || [],
        description: validatedData.description || null,
      },
    });

    if (!result.success) {
      return {
        success: false,
        message: result.message,
      };
    }

    // Revalidate and redirect
    revalidatePath(`/${userProfile.language}/protected/document/templates`);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Validation failed",
        errors: error.flatten().fieldErrors,
      };
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to create template",
    };
  }

  redirect(`/${userProfile.language}/protected/document/templates/list`);
}

/**
 * Create a new document template (without redirect)
 */
export async function createTemplateAction(formData: FormData) {
  try {
    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // Parse and validate form data
    const rawData = {
      name: formData.get("name"),
      description: formData.get("description") || undefined,
      content: formData.get("content"),
      // category: formData.get("category"),
      // tags: formData.get("tags") ? JSON.parse(formData.get("tags") as string) : [],
      is_active: formData.get("is_active") === "true",
    };

    const validatedData = createTemplateSchema.parse(rawData);

    // Validate template content
    const templateService = new DocumentTemplateService();
    const validation = templateService.validateTemplateContent(validatedData.content);

    if (!validation.isValid) {
      return {
        success: false,
        error: "Template content validation failed: " + validation.errors.join(", "),
      };
    }

    // Create template using service
    const result = await templateService.createTemplate({
      name: validatedData.name,
      content: validatedData.content,
      template_type: validatedData.template_type,
      language: validatedData.language,
      service_id: validatedData.service_id && validatedData.service_id !== "none" ? validatedData.service_id : null,
      is_active: validatedData.is_active,
      signature_required: validatedData.signature_required,
      organization_id: userProfile.organizationId,
      created_by: userProfile.id,
      metadata: {
        tags: validatedData.tags || [],
        description: validatedData.description || null,
      },
    });

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate the templates list
    revalidatePath("/protected/document/templates");

  } catch (error) {
    if (error instanceof z.ZodError) {
      const errorMessages = Object.entries(error.flatten().fieldErrors)
        .map(([field, messages]) => `${field}: ${messages?.join(", ")}`)
        .join("; ");
      return { success: false, error: `Validation failed: ${errorMessages}` };
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create template",
    };
  }

  // Redirect after successful creation - must be outside try-catch
  redirect("./list");
}
