import { Database } from "@/lib/types/database.types";

// Database types
export type DocumentSignature = Database["public"]["Tables"]["document_signatures"]["Row"];
export type DocumentSignatureInsert = Database["public"]["Tables"]["document_signatures"]["Insert"];
export type DocumentSignatureUpdate = Database["public"]["Tables"]["document_signatures"]["Update"];

// Extended types for UI
export interface DocumentSignatureWithDetails extends DocumentSignature {
  contact?: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  document?: {
    id: string;
    document_name: string;
    file_path: string;
  };
  requested_by_user?: {
    id: string;
    email: string;
  };
}

// Signature status types
export const SIGNATURE_STATUS = {
  PENDING: "pending",
  SIGNED: "signed",
  REJECTED: "rejected",
  EXPIRED: "expired",
} as const;

export type SignatureStatus = (typeof SIGNATURE_STATUS)[keyof typeof SIGNATURE_STATUS];

// Signature method types
export const SIGNATURE_METHOD = {
  ELECTRONIC: "electronic",
  WET: "wet",
  DIGITAL: "digital",
} as const;

export type SignatureMethod = (typeof SIGNATURE_METHOD)[keyof typeof SIGNATURE_METHOD];

// Form types
export interface CreateSignatureRequestData {
  documentId: string;
  contactId: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface SignDocumentData {
  signatureBlob: string;
  ipAddress?: string;
  userAgent?: string;
}

// Signature collection types
export interface SignaturePadData {
  width: number;
  height: number;
  signatureData: string; // Base64 encoded signature image
}

// Signature request filters
export interface SignatureFilters {
  status?: SignatureStatus;
  contactId?: string;
  documentId?: string;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
}

// Service response types
export interface SignatureListResponse {
  signatures: DocumentSignatureWithDetails[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Signature validation types
export interface SignatureValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Signature notification types
export interface SignatureNotificationData {
  signatureId: string;
  documentName: string;
  contactName: string;
  contactEmail: string;
  expiresAt: Date;
  signatureUrl: string;
}

// Signature token replacement types
export interface SignatureTokenData {
  contactSignature?: string;
  signatureDate?: string;
  witnessSignature?: string;
  employeeSignature?: string;
  signatureStatus?: string;
}

// Signature workflow types
export interface SignatureWorkflow {
  documentId: string;
  requiredSignatures: {
    contactId: string;
    role: "contact" | "witness" | "employee";
    required: boolean;
  }[];
  completionCallback?: (documentId: string, allSigned: boolean) => void;
}

// Signature audit types
export interface SignatureAuditLog {
  id: string;
  signatureId: string;
  action: "created" | "signed" | "rejected" | "expired" | "reminded";
  timestamp: Date;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

// Signature reminder types
export interface SignatureReminder {
  signatureId: string;
  reminderType: "initial" | "followup" | "final";
  scheduledAt: Date;
  sentAt?: Date;
  status: "pending" | "sent" | "failed";
}

// Signature template configuration
export interface SignatureTemplateConfig {
  requiresSignature: boolean;
  signatureFields: {
    contact: boolean;
    witness: boolean;
    employee: boolean;
  };
  expirationDays: number;
  reminderSchedule: {
    initial: number; // days after request
    followup: number; // days after initial
    final: number; // days before expiration
  };
}

// Signature verification types
export interface SignatureVerification {
  signatureId: string;
  isValid: boolean;
  verificationMethod: "hash" | "certificate" | "timestamp";
  verificationData: Record<string, any>;
  verifiedAt: Date;
  verifiedBy?: string;
}

// Public signature access types (for contacts to sign documents)
export interface PublicSignatureAccess {
  token: string; // Secure token for public access
  signatureId: string;
  expiresAt: Date;
  accessCount: number;
  maxAccess: number;
}

// Signature statistics types
export interface SignatureStatistics {
  totalRequests: number;
  pendingRequests: number;
  signedRequests: number;
  rejectedRequests: number;
  expiredRequests: number;
  averageSigningTime: number; // in hours
  signatureRate: number; // percentage
}
