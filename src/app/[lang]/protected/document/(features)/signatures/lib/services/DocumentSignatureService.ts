import { createClient } from "@/lib/supabase/server";
import { Database } from "@/lib/types/database.types";

type DocumentSignature = Database["public"]["Tables"]["document_signatures"]["Row"];
type DocumentSignatureInsert = Database["public"]["Tables"]["document_signatures"]["Insert"];
type DocumentSignatureUpdate = Database["public"]["Tables"]["document_signatures"]["Update"];

export interface SignatureRequest {
  documentId: string;
  contactId: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface SignatureData {
  signatureBlob: string;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

export class DocumentSignatureService {
  private static instance: DocumentSignatureService;

  private constructor() {}

  public static getInstance(): DocumentSignatureService {
    if (!DocumentSignatureService.instance) {
      DocumentSignatureService.instance = new DocumentSignatureService();
    }
    return DocumentSignatureService.instance;
  }

  private async getSupabaseClient() {
    return await createClient();
  }

  /**
   * Create a signature request for a document
   */
  async createSignatureRequest(
    request: SignatureRequest,
    organizationId: string,
    requestedBy: string
  ) {
    try {
      const supabase = await this.getSupabaseClient();
      const expiresAt = request.expiresAt || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days default

      const { data, error } = await supabase
        .from("document_signatures")
        .insert({
          organization_id: organizationId,
          document_id: request.documentId,
          contact_id: request.contactId,
          expires_at: expiresAt.toISOString(),
          requested_by: requestedBy,
          metadata: request.metadata || {},
        })
        .select()
        .single();

      if (error) {
        return { success: false, message: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to create signature request",
      };
    }
  }

  /**
   * Get signature requests for a document
   */
  async getDocumentSignatures(documentId: string, organizationId: string) {
    try {
      const supabase = await this.getSupabaseClient();
      const { data, error } = await supabase
        .from("document_signatures")
        .select(`
          *,
          contact:contacts(id, first_name, last_name, email)
        `)
        .eq("document_id", documentId)
        .eq("organization_id", organizationId)
        .order("requested_at", { ascending: false });

      if (error) {
        return { success: false, message: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get document signatures",
      };
    }
  }

  /**
   * Get signature request by ID
   */
  async getSignatureRequest(signatureId: string, organizationId: string) {
    try {
      const supabase = await this.getSupabaseClient();
      const { data, error } = await supabase
        .from("document_signatures")
        .select(`
          *,
          contact:contacts(id, first_name, last_name, email),
          document:document_attachments(id, document_name, file_path)
        `)
        .eq("id", signatureId)
        .eq("organization_id", organizationId)
        .single();

      if (error) {
        return { success: false, message: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get signature request",
      };
    }
  }

  /**
   * Sign a document
   */
  async signDocument(
    signatureId: string,
    signatureData: SignatureData,
    organizationId: string
  ) {
    try {
      const supabase = await this.getSupabaseClient();

      // First check if signature request exists and is valid
      const { data: signature, error: fetchError } = await supabase
        .from("document_signatures")
        .select("*")
        .eq("id", signatureId)
        .eq("organization_id", organizationId)
        .single();

      if (fetchError || !signature) {
        return { success: false, message: "Signature request not found" };
      }

      if (signature.signature_status !== "pending") {
        return { success: false, message: "Signature request is no longer pending" };
      }

      if (signature.expires_at && new Date(signature.expires_at) < new Date()) {
        return { success: false, message: "Signature request has expired" };
      }

      // Update signature with signed data
      const { data, error } = await supabase
        .from("document_signatures")
        .update({
          signature_status: "signed",
          signed_at: signatureData.timestamp.toISOString(),
          signature_data: signatureData.signatureBlob,
          metadata: {
            ...(signature.metadata as Record<string, any> || {}),
            ip_address: signatureData.ipAddress,
            user_agent: signatureData.userAgent,
            signed_timestamp: signatureData.timestamp.toISOString(),
          },
        })
        .eq("id", signatureId)
        .eq("organization_id", organizationId)
        .select()
        .single();

      if (error) {
        return { success: false, message: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to sign document",
      };
    }
  }

  /**
   * Reject a signature request
   */
  async rejectSignature(signatureId: string, organizationId: string, reason?: string) {
    try {
      const supabase = await this.getSupabaseClient();
      const { data, error } = await supabase
        .from("document_signatures")
        .update({
          signature_status: "rejected",
          metadata: {
            rejection_reason: reason,
            rejected_at: new Date().toISOString(),
          },
        })
        .eq("id", signatureId)
        .eq("organization_id", organizationId)
        .eq("signature_status", "pending")
        .select()
        .single();

      if (error) {
        return { success: false, message: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to reject signature",
      };
    }
  }

  /**
   * Get pending signatures for a contact
   */
  async getPendingSignaturesForContact(contactId: string, organizationId: string) {
    try {
      const supabase = await this.getSupabaseClient();
      const { data, error } = await supabase
        .from("document_signatures")
        .select(`
          *,
          document:document_attachments(id, document_name, file_path)
        `)
        .eq("contact_id", contactId)
        .eq("organization_id", organizationId)
        .eq("signature_status", "pending")
        .gt("expires_at", new Date().toISOString())
        .order("requested_at", { ascending: false });

      if (error) {
        return { success: false, message: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : "Failed to get pending signatures",
      };
    }
  }

  /**
   * Replace signature tokens in document content
   */
  replaceSignatureTokens(content: string, signatures: DocumentSignature[]): string {
    let processedContent = content;

    // Replace signature tokens based on signature data
    signatures.forEach((signature) => {
      if (signature.signature_status === "signed") {
        // Replace with actual signature data
        processedContent = processedContent.replace(
          /\{\{signature\.contact\}\}/g,
          `<div class="signature-block">
            <div class="signature-line">[Electronically Signed]</div>
            <div class="signature-date">Signed on: ${new Date(signature.signed_at!).toLocaleDateString()}</div>
          </div>`
        );
        
        processedContent = processedContent.replace(
          /\{\{signature\.date\}\}/g,
          new Date(signature.signed_at!).toLocaleDateString()
        );
        
        processedContent = processedContent.replace(
          /\{\{signature\.status\}\}/g,
          "Signed"
        );
      } else {
        // Replace with pending signature placeholders
        processedContent = processedContent.replace(
          /\{\{signature\.contact\}\}/g,
          `<div class="signature-placeholder">
            <div class="signature-line">_________________________</div>
            <div class="signature-label">Contact Signature (Pending)</div>
          </div>`
        );
        
        processedContent = processedContent.replace(
          /\{\{signature\.date\}\}/g,
          "_____________"
        );
        
        processedContent = processedContent.replace(
          /\{\{signature\.status\}\}/g,
          "Pending Signature"
        );
      }
    });

    // Replace any remaining signature tokens with placeholders
    processedContent = processedContent.replace(
      /\{\{signature\.witness\}\}/g,
      `<div class="signature-placeholder">
        <div class="signature-line">_________________________</div>
        <div class="signature-label">Witness Signature</div>
      </div>`
    );
    
    processedContent = processedContent.replace(
      /\{\{signature\.employee\}\}/g,
      `<div class="signature-placeholder">
        <div class="signature-line">_________________________</div>
        <div class="signature-label">Employee Signature</div>
      </div>`
    );

    return processedContent;
  }
}
