"use server";

import { revalidatePath } from "next/cache";
import { z } from "zod";
import { DocumentSignatureService } from "../lib/services/DocumentSignatureService";
import { auth } from "@/lib/authentication/services/AuthenticationService";

// Validation schema
const createSignatureRequestSchema = z.object({
  documentId: z.string().min(1, "Document ID is required"),
  contactId: z.string().min(1, "Contact is required"),
  expiresAt: z.date().min(new Date(), "Expiration date must be in the future"),
  message: z.string().optional(),
});

export type CreateSignatureRequestFormState = {
  success: boolean;
  message: string;
  errors?: Record<string, string[] | undefined>;
};

/**
 * Create a signature request
 */
export async function createSignatureRequest(
  prevState: CreateSignatureRequestFormState,
  formData: FormData
): Promise<CreateSignatureRequestFormState> {
  const userProfile = await auth.getCurrentUserProfile();

  if (!userProfile?.organizationId) {
    return {
      success: false,
      message: "Organization context required",
    };
  }

  try {
    // Parse and validate form data
    const rawData = {
      documentId: formData.get("documentId") as string,
      contactId: formData.get("contactId") as string,
      expiresAt: new Date(formData.get("expiresAt") as string),
      message: formData.get("message") as string || undefined,
    };

    const validatedData = createSignatureRequestSchema.parse(rawData);

    // Create signature request using service
    const signatureService = DocumentSignatureService.getInstance();
    const result = await signatureService.createSignatureRequest(
      {
        documentId: validatedData.documentId,
        contactId: validatedData.contactId,
        expiresAt: validatedData.expiresAt,
        metadata: {
          message: validatedData.message,
          requested_via: "admin_interface",
        },
      },
      userProfile.organizationId,
      userProfile.id
    );

    if (!result.success) {
      return {
        success: false,
        message: result.message || "Failed to create signature request",
      };
    }

    // Revalidate relevant paths
    revalidatePath(`/protected/document/attachments/${validatedData.documentId}`);
    revalidatePath("/protected/document/signatures");

    return {
      success: true,
      message: "Signature request created successfully",
    };

  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Validation failed",
        errors: error.flatten().fieldErrors,
      };
    }

    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to create signature request",
    };
  }
}

/**
 * Sign a document
 */
export async function signDocument(
  signatureId: string,
  signatureData: string,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    const signatureService = DocumentSignatureService.getInstance();
    const result = await signatureService.signDocument(
      signatureId,
      {
        signatureBlob: signatureData,
        ipAddress,
        userAgent,
        timestamp: new Date(),
      },
      userProfile.organizationId
    );

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate relevant paths
    revalidatePath("/protected/document/signatures");
    revalidatePath(`/protected/document/signatures/${signatureId}`);

    return {
      success: true,
      data: result.data,
      message: "Document signed successfully",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to sign document",
    };
  }
}

/**
 * Reject a signature request
 */
export async function rejectSignature(signatureId: string, reason?: string) {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    const signatureService = DocumentSignatureService.getInstance();
    const result = await signatureService.rejectSignature(
      signatureId,
      userProfile.organizationId,
      reason
    );

    if (!result.success) {
      return { success: false, error: result.message };
    }

    // Revalidate relevant paths
    revalidatePath("/protected/document/signatures");
    revalidatePath(`/protected/document/signatures/${signatureId}`);

    return {
      success: true,
      data: result.data,
      message: "Signature request rejected",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to reject signature",
    };
  }
}

/**
 * Get pending signatures for current user's contacts
 */
export async function getPendingSignatures() {
  try {
    const userProfile = await auth.getCurrentUserProfile();

    if (!userProfile?.organizationId) {
      return { success: false, error: "Organization context required" };
    }

    // This would typically get signatures for contacts associated with the current user
    // For now, we'll return a placeholder response
    return {
      success: true,
      data: [],
      message: "Pending signatures retrieved",
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get pending signatures",
    };
  }
}
