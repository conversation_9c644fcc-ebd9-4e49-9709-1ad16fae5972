"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { SignaturePad } from "@/app/[lang]/protected/document/(features)/signatures/components/SignaturePad";
import { CheckCircle, XCircle, FileSignature } from "lucide-react";
import { submitSignature, rejectSignature } from "../actions";

interface SignatureRequest {
  id: string;
  document: {
    id: string;
    document_name: string;
    file_path: string;
  };
  contact: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  expires_at: string;
  signature_status: "pending" | "signed" | "rejected";
  requested_at: string;
  organization_id: string;
}

interface SignatureCollectionFormProps {
  signatureRequest: SignatureRequest;
  lang: string;
}

export function SignatureCollectionForm({ signatureRequest, lang }: SignatureCollectionFormProps) {
  const [signatureData, setSignatureData] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectionForm, setShowRejectionForm] = useState(false);

  const handleSignatureChange = (signature: string | null) => {
    setSignatureData(signature);
    setError(null);
  };

  const handleSign = async () => {
    if (!signatureData) {
      setError("Please provide your signature before submitting.");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Get client information for audit trail
      const ipAddress = await fetch('/api/ip').then(r => r.json()).catch(() => ({ ip: 'unknown' }));
      const userAgent = navigator.userAgent;

      // Submit signature using server action
      const result = await submitSignature(
        signatureRequest.id,
        signatureData,
        ipAddress.ip,
        userAgent
      );

      if (result.success) {
        setIsCompleted(true);
      } else {
        setError(result.error || "Failed to submit signature. Please try again.");
      }
    } catch (err) {
      setError("Failed to submit signature. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!rejectionReason.trim()) {
      setError("Please provide a reason for rejection.");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Get client information for audit trail
      const ipAddress = await fetch('/api/ip').then(r => r.json()).catch(() => ({ ip: 'unknown' }));
      const userAgent = navigator.userAgent;

      // Reject signature using server action
      const result = await rejectSignature(
        signatureRequest.id,
        rejectionReason,
        ipAddress.ip,
        userAgent
      );

      if (result.success) {
        setIsCompleted(true);
      } else {
        setError(result.error || "Failed to reject signature. Please try again.");
      }
    } catch (err) {
      setError("Failed to reject signature. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isCompleted) {
    return (
      <Card className="bg-green-50 border-green-200">
        <CardContent className="pt-6">
          <div className="text-center">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-green-900 mb-2">
              {showRejectionForm ? "Signature Rejected" : "Document Signed Successfully"}
            </h3>
            <p className="text-green-700 mb-4">
              {showRejectionForm 
                ? "Your rejection has been recorded and the organization has been notified."
                : "Thank you for signing the document. The organization has been notified of your signature."
              }
            </p>
            <div className="text-sm text-green-600">
              Completed on: {new Date().toLocaleString()}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Signature Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSignature className="h-5 w-5" />
            Electronic Signature
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              By signing this document electronically, you agree that your electronic signature 
              has the same legal effect as a handwritten signature.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <Label className="text-base font-medium">
              Please sign below using your mouse or finger on touch devices:
            </Label>
            
            <SignaturePad
              onSignatureChange={handleSignatureChange}
              disabled={isSubmitting}
              width={500}
              height={200}
            />

            {error && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={handleSign}
          disabled={!signatureData || isSubmitting}
          className="flex-1"
          size="lg"
        >
          {isSubmitting ? "Submitting..." : "Sign Document"}
        </Button>

        <Button
          variant="outline"
          onClick={() => setShowRejectionForm(!showRejectionForm)}
          disabled={isSubmitting}
          className="flex-1"
          size="lg"
        >
          Reject Signature
        </Button>
      </div>

      {/* Rejection Form */}
      {showRejectionForm && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-900">Reject Signature Request</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="rejection-reason">Reason for rejection (required):</Label>
              <Textarea
                id="rejection-reason"
                placeholder="Please explain why you are rejecting this signature request..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                disabled={isSubmitting}
                rows={3}
              />
            </div>

            <div className="flex gap-4">
              <Button
                variant="destructive"
                onClick={handleReject}
                disabled={!rejectionReason.trim() || isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Confirm Rejection"}
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowRejectionForm(false);
                  setRejectionReason("");
                  setError(null);
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Legal Notice */}
      <Card className="bg-gray-50">
        <CardContent className="pt-6">
          <div className="text-sm text-gray-600 space-y-2">
            <h4 className="font-medium text-gray-900">Legal Notice:</h4>
            <ul className="list-disc list-inside space-y-1">
              <li>Your electronic signature will be legally binding</li>
              <li>This signature will be stored securely and associated with this document</li>
              <li>You can reject this signature request if you do not agree to sign</li>
              <li>A copy of the signed document will be available for your records</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
