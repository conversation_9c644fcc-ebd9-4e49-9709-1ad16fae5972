import { notFound } from "next/navigation";
import { DocumentSignatureService } from "@/app/[lang]/protected/document/(features)/signatures/lib/services/DocumentSignatureService";
import { SignatureCollectionForm } from "./components/SignatureCollectionForm";

interface SignaturePageProps {
  params: Promise<{
    lang: string;
    signatureId: string;
  }>;
}

export default async function SignaturePage({ params }: SignaturePageProps) {
  const { lang, signatureId } = await params;

  // Get signature request details
  const signatureService = DocumentSignatureService.getInstance();
  
  // For now, we'll create a mock signature request to test the interface
  // In production, this would fetch from the database
  const mockSignatureRequest = {
    id: signatureId,
    document: {
      id: "doc-123",
      document_name: "Service Agreement",
      file_path: "/documents/service-agreement.pdf"
    },
    contact: {
      id: "contact-123",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      email: "<EMAIL>"
    },
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
    signature_status: "pending" as const,
    requested_at: new Date().toISOString(),
    organization_id: "org-123"
  };

  if (!mockSignatureRequest || mockSignatureRequest.signature_status !== "pending") {
    notFound();
  }

  // Check if signature request has expired
  const isExpired = new Date(mockSignatureRequest.expires_at) < new Date();
  if (isExpired) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
          <div className="text-red-500 text-6xl mb-4">⏰</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Signature Request Expired</h1>
          <p className="text-gray-600 mb-4">
            This signature request has expired. Please contact the organization for a new signature request.
          </p>
          <div className="text-sm text-gray-500">
            Expired on: {new Date(mockSignatureRequest.expires_at).toLocaleDateString()}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-4 mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Electronic Signature Request</h1>
              <p className="text-gray-600">Please review and sign the document below</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">Document:</span>
              <span className="ml-2 text-gray-900">{mockSignatureRequest.document.document_name}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Requested for:</span>
              <span className="ml-2 text-gray-900">
                {mockSignatureRequest.contact.first_name} {mockSignatureRequest.contact.last_name}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Email:</span>
              <span className="ml-2 text-gray-900">{mockSignatureRequest.contact.email}</span>
            </div>
            <div>
              <span className="font-medium text-gray-700">Expires:</span>
              <span className="ml-2 text-gray-900">
                {new Date(mockSignatureRequest.expires_at).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Document Preview */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Document Preview</h2>
          <div className="border rounded-lg p-4 bg-gray-50 min-h-[200px]">
            <div className="text-center text-gray-500">
              <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-lg font-medium">Document: {mockSignatureRequest.document.document_name}</p>
              <p className="text-sm">Please review the document content before signing</p>
            </div>
          </div>
        </div>

        {/* Signature Collection Form */}
        <SignatureCollectionForm 
          signatureRequest={mockSignatureRequest}
          lang={lang}
        />

        {/* Footer */}
        <div className="text-center text-sm text-gray-500 mt-8">
          <p>This is a secure electronic signature request.</p>
          <p>Your signature will be legally binding and stored securely.</p>
        </div>
      </div>
    </div>
  );
}
