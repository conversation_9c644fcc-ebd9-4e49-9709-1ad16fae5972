#!/usr/bin/env node

/**
 * Simple test script to simulate the MVP functionality
 * This script will simulate updating a request status to "completed"
 * and check if a case file and document are created
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testMVP() {
  console.log('🧪 Testing Ultra Simple MVP: Case File Document Generation');
  console.log('=' .repeat(60));

  try {
    // 1. Create test data directly
    console.log('\n1️⃣ Creating test data...');

    // Create a mock request object using real request ID
    const request = {
      id: '23a254e5-89e2-4302-9178-af9479964397',
      service_id: '0bbe5ea3-0ae3-4746-8f71-7c556fd46464',
      status: 'completed',
      organization_id: '00000000-0000-0000-0000-000000000001'
    };

    // Create a mock service
    const service = {
      id: request.service_id,
      name: 'Family Visit Supervision'
    };

    // Create mock contacts
    const requestContacts = [{
      id: 'contact-123',
      relationship_type: 'family',
      contacts: {
        id: 'b898a174-f235-47a3-9af5-cb0e4c40000d',
        name: 'John Doe'
      }
    }];

    console.log('✅ Test data created:', {
      id: request.id,
      status: request.status,
      service: service?.name,
      contacts: requestContacts?.length || 0
    });

    // 2. Check template exists
    const { data: template, error: templateError } = await supabase
      .from('document_templates')
      .select('*')
      .eq('organization_id', '00000000-0000-0000-0000-000000000001')
      .eq('is_active', true)
      .ilike('name', '%service%')
      .limit(1)
      .single();

    if (templateError || !template) {
      console.error('❌ Template not found:', templateError?.message);
      return;
    }

    console.log('✅ Template found:', template.name);

    // 3. Check case files before (should be 0)
    const { data: caseFilesBefore, error: caseFilesBeforeError } = await supabase
      .from('case_files')
      .select('*')
      .eq('request_id', request.id);

    console.log(`📊 Case files before: ${caseFilesBefore?.length || 0}`);

    // 4. Check documents before (should be 0)
    const { data: documentsBefore, error: documentsBeforeError } = await supabase
      .from('document_attachments')
      .select('*')
      .eq('attached_to_type', 'case_file');

    console.log(`📄 Documents before: ${documentsBefore?.length || 0}`);

    // 5. Simulate the MVP trigger by calling our function directly
    console.log('\n2️⃣ Simulating MVP trigger...');
    console.log('⚠️  Note: This would normally be triggered by updateRequestStatus()');
    console.log('⚠️  For this test, we\'ll simulate the case file creation manually');

    // Simulate case file creation
    const primaryContact = requestContacts?.find(rc =>
      rc.relationship_type === "primary" || rc.relationship_type === "family"
    )?.contacts || requestContacts?.[0]?.contacts;

    const { data: caseFile, error: caseFileError } = await supabase
      .from('case_files')
      .insert({
        organization_id: '00000000-0000-0000-0000-000000000001',
        request_id: request.id,
        case_number: `CF-${Date.now()}`,
        status: 'opening',
        created_by: '00000000-0000-0000-0000-000000000000', // Real user ID
        metadata: {
          auto_generated: true,
          generated_from_request: request.id,
          generated_at: new Date().toISOString(),
          primary_contact_id: primaryContact?.id || null,
          service_id: request.service_id,
        },
      })
      .select()
      .single();

    if (caseFileError || !caseFile) {
      console.error('❌ Failed to create case file:', caseFileError?.message);
      return;
    }

    console.log('✅ Case file created:', caseFile.case_number);

    // 6. Generate document
    console.log('\n3️⃣ Generating document...');

    // Simple token replacement
    let content = template.content;
    const contactName = primaryContact?.name || "Contact Name";
    const currentDate = new Date().toLocaleDateString();
    const orgName = "RQRSDA Montreal";

    content = content.replace(/\{\{contact\.name\}\}/g, contactName);
    content = content.replace(/\{\{case\.case_number\}\}/g, caseFile.case_number);
    content = content.replace(/\{\{date\.current\}\}/g, currentDate);
    content = content.replace(/\{\{organization\.name\}\}/g, orgName);

    // Save document with contact linkage
    const { data: document, error: docError } = await supabase
      .from('document_attachments')
      .insert({
        organization_id: caseFile.organization_id,
        attached_to_type: 'case_file',
        attached_to_id: caseFile.id,
        contact_id: primaryContact?.id || null, // Link to the actual contact
        document_name: `Service Agreement - ${primaryContact?.name || "Contact"} - ${caseFile.case_number}`,
        file_path: `/generated/${caseFile.id}-service-agreement.html`,
        document_type: 'HTML',
        file_size: content.length,
        attachment_type: 'generated',
        uploaded_by: caseFile.created_by,
        template_id: template.id, // Link to the template used
        metadata: {
          template_id: template.id,
          template_name: template.name,
          generated_content: content,
          generated_at: new Date().toISOString(),
          auto_generated: true,
          source_request_id: request.id,
          contact_name: primaryContact?.name,
          relationship_type: primaryContact?.relationship_type,
        },
      })
      .select()
      .single();

    if (docError || !document) {
      console.error('❌ Failed to create document:', docError?.message);
      return;
    }

    console.log('✅ Document generated:', document.document_name);

    // 7. Verify results
    console.log('\n4️⃣ Verifying results...');

    const { data: caseFilesAfter } = await supabase
      .from('case_files')
      .select('*')
      .eq('request_id', request.id);

    const { data: documentsAfter } = await supabase
      .from('document_attachments')
      .select('*')
      .eq('attached_to_id', caseFile.id);

    console.log(`📊 Case files after: ${caseFilesAfter?.length || 0}`);
    console.log(`📄 Documents after: ${documentsAfter?.length || 0}`);

    // Verify contact linkage
    const documentWithContact = documentsAfter?.[0];
    if (documentWithContact?.contact_id) {
      console.log(`🔗 Document linked to contact: ${documentWithContact.contact_id}`);
      console.log(`📝 Document name: ${documentWithContact.document_name}`);
    } else {
      console.log(`⚠️  Document not linked to any contact`);
    }

    // 8. Show generated content preview
    console.log('\n5️⃣ Generated document preview:');
    console.log('-'.repeat(40));
    console.log(content.substring(0, 200) + '...');
    console.log('-'.repeat(40));

    console.log('\n🎉 MVP TEST SUCCESSFUL!');
    console.log('✅ Request completed → Case file created → Document generated');
    console.log(`✅ Case file: ${caseFile.case_number}`);
    console.log(`✅ Document: ${document.document_name}`);
    console.log(`✅ Contact: ${contactName}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testMVP().then(() => {
  console.log('\n🏁 Test completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test crashed:', error);
  process.exit(1);
});
